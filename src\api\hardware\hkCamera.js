import request from '@/utils/request'

// 查询摄像头分页列表
export function listCamera(query) {
    // 将前端分页参数转换为后端需要的格式
    const params = {
        ...query,
        current: query.pageNum || 1,
        size: query.pageSize || 10
    }
    
    // 删除前端的分页参数，避免重复
    delete params.pageNum
    delete params.pageSize
    
    return request({
        url: '/hardware/hkCamera',
        method: 'get',
        params: params
    })
}

// 根据ID查询摄像头详情
export function getCamera(id) {
    return request({
        url: '/hardware/hkCamera/' + id,
        method: 'get'
    })
}

// 新增摄像头
export function addCamera(data) {
    return request({
        url: '/hardware/hkCamera',
        method: 'post',
        data: data
    })
}

// 修改摄像头
export function updateCamera(data) {
    return request({
        url: '/hardware/hkCamera',
        method: 'put',
        data: data
    })
}

// 删除摄像头
export function delCamera(id) {
    return request({
        url: '/hardware/hkCamera/' + id,
        method: 'delete'
    })
}
