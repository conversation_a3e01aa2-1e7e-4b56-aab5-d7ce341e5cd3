<template>
  <div class="app-container w-full h-full">
    <div class="w-full h-full rounded" ref="cameraVideo"></div>
  </div>
</template>
<script setup>
import DPlayer from 'dplayer';
import Hls from "hls.js";
import { onMounted, ref } from "vue";

const cameraVideo = ref()
const initializePlayer = (container, url) => {
  new DPlayer({
    container: container,
    live: true,
    autoplay: true,
    theme: "#0093ff",
    loop: true,
    lang: "zh-cn",
    screenshot: true,
    hotkey: true,
    preload: "auto",
    volume: 0.7,
    video: {
      url: url,
      type: "customHls",
      customType: {
        customHls: (video, player) => {
          const hls = new Hls();
          hls.loadSource(video.src);
          hls.attachMedia(video);
        },
      },
    },
    mutex: false,
    pluginOptions: {
      hls: {
        // hls config
      },
    },
  });
};
onMounted(() => {
  initializePlayer(cameraVideo.value, 'http://*************:8800/hls/0/index.m3u8');
});
</script>
