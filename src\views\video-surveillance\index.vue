<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--监控分类-->
      <el-col :span="4" :xs="24">
        <div class="head-container flex flex-col gap-2 items-center">
          <el-segmented v-model="deviceStatusValue" :options="monitor_device_status" size="small" />
          <el-input v-model="deptName" placeholder="请输入关键字" clearable prefix-icon="Search"
            style="margin-bottom: 20px" />
        </div>
        <div class="head-container">
          <el-tree :data="treeOptions" :props="{ label: 'label', children: 'children' }" :expand-on-click-node="false"
            :filter-node-method="filterNode" ref="treeRef" node-key="id" highlight-current default-expand-all
            @node-click="handleNodeClick" />
        </div>
      </el-col>
      <!--监控列表-->
      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="设备名称" prop="userName">
            <el-input v-model="queryParams.userName" placeholder="请输入设备名称" clearable style="width: 240px"
              @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <el-row :gutter="10" justify="end" class="mb8">
          <el-col :span="1.5">
            <el-segmented v-model="tableModelOptionsValue" :options="tableModelOptions">
              <template #default="{ item }">
                <div class="flex flex-col items-center gap-2 p-2">
                  <el-icon size="20">
                    <component :is="item.icon" />
                  </el-icon>
                </div>
              </template>
            </el-segmented>
          </el-col>
        </el-row>
        <template v-if="tableModelOptionsValue === 'grid'">
          <div class="grid grid-cols-4 gap-1">
            <div v-for="it in paginatedCameraList" :key="it.userId" @click="handleGetCameraInfo(it)"
              class="p-4 w-56 h-56 cursor-pointer flex flex-col items-center">
              <el-image class="w-full h-full" :src="it.imageUrl" :fit="fit" />
              <div class="text-center mt-2">{{ it.userName }}</div>
            </div>
          </div>
        </template>
        <template v-else-if="tableModelOptionsValue === 'list'">
          <el-table v-loading="loading" :data="paginatedCameraList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column label="站点名称" align="center" key="userId" prop="userId" :show-overflow-tooltip="true" />
            <el-table-column label="摄像头名称" align="center" key="userName" prop="userName"
              :show-overflow-tooltip="true" />
            <el-table-column label="摄像头编号" align="center" key="nickName" prop="nickName"
              :show-overflow-tooltip="true" />
            <el-table-column label="安装地址" align="center" key="deptName" prop="dept.deptName"
              :show-overflow-tooltip="true" />
            <el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
              <template #default="scope">
                <el-tooltip content="查看" placement="top">
                  <el-button link type="primary" icon="View" @click="handleGetCameraInfo(scope.row)"
                    v-hasPermi="['video-surveillance:view']"></el-button>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize" @pagination="getList" />
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="User">
import { ref, reactive, toRefs, watch, getCurrentInstance, computed } from 'vue';
import { useRouter } from 'vue-router';
import { Grid, List } from '@element-plus/icons-vue';
import camera from '@/assets/images/camera.jpg';
const router = useRouter();
const { proxy } = getCurrentInstance();
const { monitor_device_status } = proxy.useDict("monitor_device_status");
const deviceStatusValue = ref('全部');
const cameraList = ref([]);
const loading = ref(false);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref([]);
const deptName = ref("");
const treeOptions = ref([]);

const tableModelOptionsValue = ref('grid');
const tableModelOptions = reactive([
  {
    value: 'grid',
    icon: Grid,
  },
  {
    value: 'list',
    icon: List,
  },
]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: undefined,
    phonenumber: undefined,
    status: undefined,
    deptId: undefined,
  },
});

const { queryParams } = toRefs(data);

/** 通过条件过滤节点  */
const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
};

/** 根据名称筛选部门树 */
watch(deptName, val => {
  proxy.$refs["treeRef"].filter(val);
});

/** 查询监控类型下拉树结构 */
function getDeptTree() {
  treeOptions.value = [
    {
      id: 1,
      label: "门诊楼",
      children: [
        {
          id: 2,
          label: "门诊一楼",
        },
        {
          id: 3,
          label: "门诊二楼",
        },
      ],
    },
    {
      id: 4,
      label: "住院部",
      children: [
        {
          id: 5,
          label: "住院部一楼",
        },
        {
          id: 6,
          label: "住院部二楼",
        },
      ],
    },
    {
      id: 7,
      label: "急诊科",
    },
    {
      id: 8,
      label: "手术室",
    },
    {
      id: 9,
      label: "重症监护室",
    },
  ];
}

/** 查询摄像头列表 */
function getList() {
  loading.value = true;
  setTimeout(() => {
    const departments = [
      '门诊一楼', '门诊二楼', '住院部一楼', '住院部二楼', '急诊科', '手术室', '重症监护室'
    ];
    const cameraNames = [
      '大厅摄像头', '走廊摄像头', '入口摄像头', '病房摄像头', '停车场摄像头', '楼梯间摄像头', '电梯摄像头'
    ];
    cameraList.value = Array.from({ length: 50 }, (v, i) => ({
      userId: `${i + 1}`,
      userName: `${departments[i % departments.length]} ${cameraNames[i % cameraNames.length]}`,
      nickName: `CAM${String(i + 1).padStart(3, '0')}`,
      dept: { deptName: departments[i % departments.length] },
      imageUrl: camera,
    }));
    total.value = cameraList.value.length;
    loading.value = false;
  }, 1000);
}

/** 获取当前页的数据 */
const paginatedCameraList = computed(() => {
  const start = (queryParams.value.pageNum - 1) * queryParams.value.pageSize;
  const end = start + queryParams.value.pageSize;
  return cameraList.value.slice(start, end).filter(item => {
    const matchesDept = queryParams.value.deptId ? item.dept.deptName.includes(getDeptNameById(queryParams.value.deptId)) : true;
    const matchesName = queryParams.value.userName ? item.userName.includes(queryParams.value.userName) : true;
    return matchesDept && matchesName;
  });
});

/** 根据部门ID获取部门名称 */
function getDeptNameById(id) {
  const findDept = (nodes, id) => {
    for (const node of nodes) {
      if (node.id === id) return node.label;
      if (node.children) {
        const result = findDept(node.children, id);
        if (result) return result;
      }
    }
    return null;
  };
  return findDept(treeOptions.value, id);
}

/** 节点单击事件 */
function handleNodeClick(data) {
  queryParams.value.deptId = data.id;
  handleQuery();
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  queryParams.value.deptId = undefined;
  proxy.$refs.treeRef.setCurrentKey(null);
  handleQuery();
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.userId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 跳转至监控详情页面 */
function handleGetCameraInfo(row) {
  router.push({
    path: `/cctv/${row.userId}`,
  });
}

getDeptTree();
getList();
</script>

<style scoped>
.grid-cols-4 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
}
</style>
