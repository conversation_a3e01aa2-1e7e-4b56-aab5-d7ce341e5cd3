<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--监控列表-->
      <el-col :span="24" :xs="24">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="设备名称" prop="userName">
            <el-input v-model="queryParams.userName" placeholder="请输入设备名称" clearable style="width: 240px"
              @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <el-row :gutter="10" justify="end" class="mb8">
          <el-col :span="1.5">
            <el-segmented v-model="tableModelOptionsValue" :options="tableModelOptions">
              <template #default="{ item }">
                <div class="flex flex-col items-center gap-2 p-2">
                  <el-icon size="20">
                    <component :is="item.icon" />
                  </el-icon>
                </div>
              </template>
            </el-segmented>
          </el-col>
        </el-row>
        <template v-if="tableModelOptionsValue === 'grid'">
          <div class="grid grid-cols-4 gap-4">
            <div v-for="it in paginatedCameraList" :key="it.userId" @click="handleGetCameraInfo(it)"
              class="p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 flex flex-col items-center justify-center min-h-[120px]">
              <div class="text-center font-medium">{{ it.userName }}</div>
              <div class="text-center text-sm text-gray-500 mt-1">{{ it.nickName }}</div>
            </div>
          </div>
        </template>
        <template v-else-if="tableModelOptionsValue === 'list'">
          <el-table v-loading="loading" :data="paginatedCameraList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column label="站点名称" align="center" key="userId" prop="userId" :show-overflow-tooltip="true" />
            <el-table-column label="摄像头名称" align="center" key="userName" prop="userName"
              :show-overflow-tooltip="true" />
            <el-table-column label="摄像头编号" align="center" key="nickName" prop="nickName"
              :show-overflow-tooltip="true" />
            <el-table-column label="安装地址" align="center" key="deptName" prop="dept.deptName"
              :show-overflow-tooltip="true" />
            <el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
              <template #default="scope">
                <el-tooltip content="查看" placement="top">
                  <el-button link type="primary" icon="View" @click="handleGetCameraInfo(scope.row)"
                    v-hasPermi="['video-surveillance:view']"></el-button>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize" @pagination="getList" />
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="User">
import { ref, reactive, toRefs, watch, getCurrentInstance, computed } from 'vue';
import { useRouter } from 'vue-router';
import { Grid, List } from '@element-plus/icons-vue';
import { listCamera } from '@/api/hardware/hkCamera';
const router = useRouter();
const { proxy } = getCurrentInstance();

const cameraList = ref([]);
const loading = ref(false);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref([]);

const tableModelOptionsValue = ref('list');
const tableModelOptions = reactive([
    {
    value: 'list',
    icon: List,
  },
  {
    value: 'grid',
    icon: Grid,
  },

]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: undefined,
    phonenumber: undefined,
    status: undefined,
  },
});

const { queryParams } = toRefs(data);





/** 查询摄像头列表 */
function getList() {
  loading.value = true;
  listCamera(queryParams.value).then(response => {
    // 处理后端返回的数据结构
    if (response.data && response.data.records) {
      // 将后端数据字段映射到前端需要的格式
      cameraList.value = response.data.records.map(item => ({
        userId: item.id,
        userName: item.cameraName,
        nickName: item.cameraIndexOde,
        dept: { deptName: item.cameraName }, // 如果后端有部门信息，可以调整这里
        // 保留原始数据以备后用
        originalData: item
      }));
      total.value = response.data.total || 0;
    } else {
      cameraList.value = [];
      total.value = 0;
    }
    loading.value = false;
  }).catch(error => {
    console.error('获取摄像头列表失败:', error);
    cameraList.value = [];
    total.value = 0;
    loading.value = false;
  });
}

/** 获取当前页的数据 */
const paginatedCameraList = computed(() => {
  // 由于后端已经处理了分页，直接返回当前页的数据
  // 如果需要前端过滤，可以在这里添加过滤逻辑
  return cameraList.value.filter(item => {
    const matchesName = queryParams.value.userName ? item.userName.includes(queryParams.value.userName) : true;
    return matchesName;
  });
});



/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.userId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 跳转至监控详情页面 */
function handleGetCameraInfo(row) {
  router.push({
    path: `/cctv/${row.userId}`,
  });
}

getList();
</script>

<style scoped>
.grid-cols-4 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
}
</style>
