<template>
  <div class="item">
    <div v-for="(raf, index) in rafs" :key="index">
      <div class="left-3">
        <div class="left">
          <div class="vertical-text">
            <p>{{ raf.name }}</p>
          </div>
        </div>
        <div class="right" :ref="raf.ref"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import DPlayer from 'dplayer'
  import Hls from 'hls.js'
  import { ref, onMounted } from 'vue'
  import { listCamera, getPreviewURLs } from '@/api/hardware/hkCamera'

  const rafs = [
    {
      ref: ref(),
      name: ref(''),
    },
    {
      ref: ref(),
      name: ref(''),
    },
    {
      ref: ref(),
      name: ref(''),
    },
    {
      ref: ref(),
      name: ref(''),
    },
  ]
  const initializePlayer = (container, url) => {
    new DPlayer({
      container: container,
      live: true,
      autoplay: true,
      theme: '#0093ff',
      loop: true,
      lang: 'zh-cn',
      screenshot: true,
      hotkey: true,
      mutex: false,
      preload: 'auto',
      volume: 0,
      video: {
        url: url,
        type: 'customHls',
        customType: {
          customHls: (video, player) => {
            const hls = new Hls()
            hls.loadSource(video.src)
            hls.attachMedia(video)
          },
        },
      },
      pluginOptions: {
        hls: {
          // hls config
        },
      },
    })
  }

  //随机取4条数据
  function getRandomElements(arr, num) {
    const result = new Array(num)
    let len = arr.length
    const taken = new Array(len)

    if (num > len) {
      throw new RangeError('getRandomElements: more elements taken than available')
    }

    while (num--) {
      const x = Math.floor(Math.random() * len)
      result[num] = arr[x in taken ? taken[x] : x]
      taken[x] = --len in taken ? taken[len] : len
    }

    return result
  }

  onMounted(async () => {
    // 获取摄像头数据，只取前4条
    const queryParams = {
      pageNum: 1,
      pageSize: 4
    }

    try {
      const response = await listCamera(queryParams);
      if (response.data && response.data.records) {
        const data = response.data.records
        // 如果数据不足4条，就用现有的数据
        const camerasToShow = data.length >= 4 ? getRandomElements(data, 4) : data

        // 为每个摄像头获取真实的视频流URL
        for (let index = 0; index < camerasToShow.length && index < rafs.length; index++) {
          const item = camerasToShow[index];
          rafs[index].name.value = item.cameraName;

          try {
            // 获取真实的视频流URL
            if (item.cameraIndexOde) {
              const urlResponse = await getPreviewURLs(item.cameraIndexOde);
              if (urlResponse.data && urlResponse.data.url) {
                initializePlayer(rafs[index].ref.value[0], urlResponse.data.url);
              } else {
                // 如果获取URL失败，使用测试流
                initializePlayer(rafs[index].ref.value[0], 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8');
              }
            } else {
              // 如果没有摄像头编号，使用测试流
              initializePlayer(rafs[index].ref.value[0], 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8');
            }
          } catch (urlError) {
            console.error(`获取摄像头 ${item.cameraName} 视频流失败:`, urlError);
            // 出错时使用测试流
            initializePlayer(rafs[index].ref.value[0], 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8');
          }
        }
      }
    } catch (error) {
      console.error('获取摄像头数据失败:', error);
      // 如果接口失败，可以显示默认名称和测试流
      rafs.forEach((raf, index) => {
        raf.name.value = `摄像头 ${index + 1}`;
        initializePlayer(raf.ref.value[0], 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8');
      });
    }
  })
</script>

<style scoped>
  .item {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px; /* Adjust the gap between items as needed */
    font-size: 12px;
  }

  .left-3 {
    display: flex;
    flex: 1;
  }

  .left {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 30px;
    height: 83px;
    background-image: url('@/assets/images/left-3-1.png');
  }

  .vertical-text {
    writing-mode: vertical-rl; /* 竖直写字 */
    text-align: center;
    color: #fff;
  }

  .right {
    width: 140px; /* 右边 div 宽度 */
    height: 83px;
    background-size: cover; /* 图片尺寸适应 */
    background-position: center; /* 图片居中 */
  }
</style>
