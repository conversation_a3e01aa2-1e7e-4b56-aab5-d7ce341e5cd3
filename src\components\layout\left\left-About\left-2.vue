<template>
  <div class="item">
    <div v-for="(raf, index) in rafs" :key="index">
      <div class="left-3">
        <div class="left">
          <div class="vertical-text">
            <p>{{ raf.name }}</p>
          </div>
        </div>
        <div class="right" :ref="raf.ref"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import DPlayer from 'dplayer'
  import Hls from 'hls.js'
  import { ref, onMounted } from 'vue'
  import { listCamera } from '@/api/hardware/hkCamera'

  const rafs = [
    {
      ref: ref(),
      name: ref(''),
    },
    {
      ref: ref(),
      name: ref(''),
    },
    {
      ref: ref(),
      name: ref(''),
    },
    {
      ref: ref(),
      name: ref(''),
    },
  ]
  const initializePlayer = (container, url) => {
    new DPlayer({
      container: container,
      live: true,
      autoplay: true,
      theme: '#0093ff',
      loop: true,
      lang: 'zh-cn',
      screenshot: true,
      hotkey: true,
      mutex: false,
      preload: 'auto',
      volume: 0,
      video: {
        url: url,
        type: 'customHls',
        customType: {
          customHls: (video, player) => {
            const hls = new Hls()
            hls.loadSource(video.src)
            hls.attachMedia(video)
          },
        },
      },
      pluginOptions: {
        hls: {
          // hls config
        },
      },
    })
  }

  //随机取4条数据
  function getRandomElements(arr, num) {
    const result = new Array(num)
    let len = arr.length
    const taken = new Array(len)

    if (num > len) {
      throw new RangeError('getRandomElements: more elements taken than available')
    }

    while (num--) {
      const x = Math.floor(Math.random() * len)
      result[num] = arr[x in taken ? taken[x] : x]
      taken[x] = --len in taken ? taken[len] : len
    }

    return result
  }

  onMounted(() => {
    // 获取摄像头数据，只取前4条
    const queryParams = {
      pageNum: 1,
      pageSize: 4
    }

    listCamera(queryParams).then((response) => {
      if (response.data && response.data.records) {
        const data = response.data.records
        // 如果数据不足4条，就用现有的数据
        const camerasToShow = data.length >= 4 ? getRandomElements(data, 4) : data

        camerasToShow.forEach((item, index) => {
          if (index < rafs.length) {
            rafs[index].name.value = item.cameraName
            // 这里仍然使用测试流，如果后端有真实的流地址，可以使用 item.streamUrl 或类似字段
            initializePlayer(
              rafs[index].ref.value[0],
              'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8'
            )
          }
        })
      }
    }).catch(error => {
      console.error('获取摄像头数据失败:', error)
      // 如果接口失败，可以显示默认名称
      rafs.forEach((raf, index) => {
        raf.name.value = `摄像头 ${index + 1}`
      })
    })
  })
</script>

<style scoped>
  .item {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px; /* Adjust the gap between items as needed */
    font-size: 12px;
  }

  .left-3 {
    display: flex;
    flex: 1;
  }

  .left {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 30px;
    height: 83px;
    background-image: url('@/assets/images/left-3-1.png');
  }

  .vertical-text {
    writing-mode: vertical-rl; /* 竖直写字 */
    text-align: center;
    color: #fff;
  }

  .right {
    width: 140px; /* 右边 div 宽度 */
    height: 83px;
    background-size: cover; /* 图片尺寸适应 */
    background-position: center; /* 图片居中 */
  }
</style>
